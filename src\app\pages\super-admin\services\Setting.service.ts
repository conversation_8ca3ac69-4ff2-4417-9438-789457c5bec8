import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { environment } from 'src/environments/environment';

@Injectable({
  providedIn: 'root'
})
export class  settingService {
  apiUrl = `${environment.apiUrl}`;

  constructor(private http: HttpClient) { }

   getAllRolls(): Observable<any> {
    const params = new HttpParams()
      .set('limit', '100')
      .set('offset', '0')
      .set('sort', 'ASC')
      .set('sortBy', 'id');

    return this.http.get<any>(`${environment.apiUrl}/authorization/list-roles`, { params });
  }

  updateRolePermissions(roleId: number, permissions: string[]): Observable<any> {
    const body = {
      permissions: permissions
    };

    return this.http.put<any>(`${environment.apiUrl}/authorization/roles/${roleId}/permissions`, body);
  }

}
