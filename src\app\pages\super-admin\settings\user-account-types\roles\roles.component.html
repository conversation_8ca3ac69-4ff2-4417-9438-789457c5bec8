<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="d-flex align-items-center">
              <i class="fas fa-user-tag text-warning me-3 fs-2"></i>
              <div>
                <h3 class="mb-0">Roles Management</h3>
                <span class="text-muted fs-6">Create and assign user roles with specific permissions</span>
              </div>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm me-2" (click)="goBack()">
              <i class="fas fa-arrow-left me-2"></i>
              Back
            </button>
            <button class="btn btn-warning btn-sm" (click)="addRole()">
              <i class="fas fa-plus me-2"></i>
              Add Role
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Roles Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">All Roles</h3>
          <div class="card-toolbar">
            <div class="d-flex align-items-center">
              <div class="position-relative me-3">
                <i class="fas fa-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                <input type="text" class="form-control form-control-sm ps-10" placeholder="Search roles...">
              </div>
              <select class="form-select form-select-sm" style="width: auto;">

                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-row-dashed table-row-gray-300 gy-7">
              <thead>
                <tr class="fw-bold fs-6 text-gray-800">
                  <th>Role</th>



                  <th>Created Date</th>
                  <th class="text-end">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngIf="roles.length === 0">
                  <td colspan="3" class="text-center py-5">
                    <div class="text-muted">
                      <i class="fas fa-inbox fs-2x mb-3"></i>
                      <div>No roles found</div>
                    </div>
                  </td>
                </tr>
                <tr *ngFor="let role of roles">
                  <td>
                    <div class="d-flex align-items-center">

                      <div>
                        <div class="fw-bold text-gray-800">{{ role.name }}</div>

                      </div>
                    </div>
                  </td>



                  <td class="text-muted">{{ (role.createdAt | date:'short') }}</td>
                  <td class="text-end">
                    <div class="dropdown position-relative">
                      <button class="btn btn-sm btn-light btn-active-light-primary" type="button"
                        (click)="toggleDropdown(role.id)">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu" [class.show]="isDropdownOpen(role.id)"
                        [style.display]="isDropdownOpen(role.id) ? 'block' : 'none'">
                        <li><button class="dropdown-item" type="button"
                            (click)="editRole(role.id); toggleDropdown(role.id)">
                            <i class="fas fa-edit me-2"></i>Edit
                          </button></li>
                        <li><button class="dropdown-item" type="button"
                            (click)="viewPermissions(role.id); toggleDropdown(role.id)">
                            <i class="fas fa-shield-alt me-2"></i>Permissions
                          </button></li>


                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<!-- Permissions Modal -->
<div class="modal fade" id="permissionsModal" tabindex="-1" aria-labelledby="permissionsModalLabel" aria-hidden="true">
  <div class="modal-dialog modal-lg">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="permissionsModalLabel">
          <i class="fas fa-shield-alt text-primary me-2"></i>
          Role Permissions: {{ selectedRole?.name }}
        </h5>
        <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <div class="row" *ngIf="selectedRole?.permissions && selectedRole.permissions.length > 0">
          <div class="col-12">
            <div class="form-check mb-3">
              <input class="form-check-input" type="checkbox" id="selectAll" [checked]="areAllPermissionsSelected()"
                (change)="toggleAllPermissions($event)">
              <label class="form-check-label fw-bold" for="selectAll">
                Select All Permissions
              </label>
            </div>
            <hr>
          </div>
          <div class="col-md-6 mb-3" *ngFor="let permission of selectedRole.permissions; let i = index">
            <div class="card permission-card">
              <div class="card-body p-3">
                <div class="form-check">
                  <input class="form-check-input" type="checkbox" [id]="'permission_' + i"
                    [(ngModel)]="permission.isSelected" (change)="onPermissionChange()">
                  <label class="form-check-label" [for]="'permission_' + i">
                    <div class="fw-bold text-gray-800">{{ permission.name || permission.permissionName }}</div>
                    <div class="text-muted fs-7" *ngIf="permission.description">{{ permission.description }}</div>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="text-center py-5" *ngIf="!selectedRole?.permissions || selectedRole.permissions.length === 0">
          <i class="fas fa-shield-alt fs-2x text-muted mb-3"></i>
          <div class="text-muted">No permissions found for this role</div>
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
          <i class="fas fa-times me-2"></i>Cancel
        </button>
        <button type="button" class="btn btn-primary" (click)="savePermissions()">
          <i class="fas fa-save me-2"></i>Save Changes
        </button>
      </div>
    </div>
  </div>
</div>