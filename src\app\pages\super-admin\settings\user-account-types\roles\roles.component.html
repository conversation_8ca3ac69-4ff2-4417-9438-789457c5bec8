<div class="container-fluid">
  <!-- Page Header -->
  <div class="row mb-5">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <div class="card-title">
            <div class="d-flex align-items-center">
              <i class="fas fa-user-tag text-warning me-3 fs-2"></i>
              <div>
                <h3 class="mb-0">Roles Management</h3>
                <span class="text-muted fs-6">Create and assign user roles with specific permissions</span>
              </div>
            </div>
          </div>
          <div class="card-toolbar">
            <button class="btn btn-secondary btn-sm me-2" (click)="goBack()">
              <i class="fas fa-arrow-left me-2"></i>
              Back
            </button>
            <button class="btn btn-warning btn-sm" (click)="addRole()">
              <i class="fas fa-plus me-2"></i>
              Add Role
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Roles Table -->
  <div class="row">
    <div class="col-12">
      <div class="card">
        <div class="card-header">
          <h3 class="card-title">All Roles</h3>
          <div class="card-toolbar">
            <div class="d-flex align-items-center">
              <div class="position-relative me-3">
                <i class="fas fa-search position-absolute top-50 start-0 translate-middle-y ms-3 text-muted"></i>
                <input type="text" class="form-control form-control-sm ps-10" placeholder="Search roles...">
              </div>
              <select class="form-select form-select-sm" style="width: auto;">

                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>
        </div>
        <div class="card-body">
          <div class="table-responsive">
            <table class="table table-row-dashed table-row-gray-300 gy-7">
              <thead>
                <tr class="fw-bold fs-6 text-gray-800">
                  <th>Role</th>



                  <th>Created Date</th>
                  <th class="text-end">Actions</th>
                </tr>
              </thead>
              <tbody>
                <tr *ngIf="roles.length === 0">
                  <td colspan="3" class="text-center py-5">
                    <div class="text-muted">
                      <i class="fas fa-inbox fs-2x mb-3"></i>
                      <div>No roles found</div>
                    </div>
                  </td>
                </tr>
                <tr *ngFor="let role of roles">
                  <td>
                    <div class="d-flex align-items-center">
                      <div class="symbol symbol-45px me-3">
                        <div class="symbol-label" [ngClass]="getRoleBadgeClass(role.color)">
                          <i class="fas fa-user-tag fs-4"></i>
                        </div>
                      </div>
                      <div>
                        <div class="fw-bold text-gray-800">{{ role.name || role.roleName || 'N/A' }}</div>
                        <div class="text-muted fs-7">{{ role.description || role.roleDescription || '' }}</div>
                      </div>
                    </div>
                  </td>



                  <td class="text-muted">{{ role.createdAt || role.created_at || role.dateCreated || 'N/A' }}</td>
                  <td class="text-end">
                    <div class="dropdown">
                      <button class="btn btn-sm btn-light btn-active-light-primary" type="button"
                        data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                      </button>
                      <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" (click)="editRole(role.id)">
                            <i class="fas fa-edit me-2"></i>Edit
                          </a></li>
                        <li><a class="dropdown-item" href="#" (click)="viewPermissions(role.id)">
                            <i class="fas fa-shield-alt me-2"></i>Permissions
                          </a></li>
                        <li>
                          <hr class="dropdown-divider">
                        </li>
                        <li><a class="dropdown-item text-danger" href="#" (click)="deleteRole(role.id)">
                            <i class="fas fa-trash me-2"></i>Delete
                          </a></li>
                      </ul>
                    </div>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>