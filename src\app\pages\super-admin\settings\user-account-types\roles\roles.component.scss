// Roles component styling using Metronic design
.card {
  border-radius: 12px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  border: 1px solid #e1e3ea;
}

.card-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e3ea;
  padding: 1.5rem;

  .card-title {
    margin-bottom: 0;

    h3 {
      color: #2d3748;
      font-weight: 700;
    }
  }

  .card-toolbar {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }
}

.card-body {
  padding: 1.5rem;
}

// Table styling
.table {
  margin-bottom: 0;

  thead th {
    border-bottom: 2px solid #e1e3ea;
    padding: 1rem 0.75rem;
    font-weight: 600;
    color: #5e6278;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
  }

  tbody td {
    padding: 1rem 0.75rem;
    border-bottom: 1px solid #f1f1f2;
    vertical-align: middle;
  }

  tbody tr:hover {
    background-color: #f8f9fa;
  }
}

// Role symbol styling
.symbol {
  &.symbol-45px {
    width: 45px;
    height: 45px;

    .symbol-label {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}

// Badge styling
.badge {
  font-size: 0.75rem;
  font-weight: 600;
  padding: 0.5rem 0.75rem;
  border-radius: 6px;

  &.badge-light-success {
    background-color: rgba(25, 135, 84, 0.1);
    color: #198754;
  }

  &.badge-light-danger {
    background-color: rgba(220, 53, 69, 0.1);
    color: #dc3545;
  }

  &.badge-light-warning {
    background-color: rgba(255, 193, 7, 0.1);
    color: #ffc107;
  }

  &.badge-light-primary {
    background-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;
  }

  &.badge-light-info {
    background-color: rgba(13, 202, 240, 0.1);
    color: #0dcaf0;
  }

  &.badge-light-secondary {
    background-color: rgba(108, 117, 125, 0.1);
    color: #6c757d;
  }
}

// Button styling
.btn {
  border-radius: 8px;
  font-weight: 500;

  &.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
  }

  &.btn-light {
    background-color: #f8f9fa;
    border-color: #f8f9fa;
    color: #5e6278;

    &:hover, &.btn-active-light-primary:hover {
      background-color: #e9ecef;
      border-color: #e9ecef;
      color: #0d6efd;
    }
  }

  &.btn-light-primary {
    background-color: rgba(13, 110, 253, 0.1);
    border-color: rgba(13, 110, 253, 0.1);
    color: #0d6efd;

    &:hover {
      background-color: rgba(13, 110, 253, 0.2);
      border-color: rgba(13, 110, 253, 0.2);
    }
  }
}

// Dropdown styling
.dropdown {
  position: relative;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  z-index: 1000;
  min-width: 160px;
  padding: 0.5rem 0;
  margin: 0.125rem 0 0;
  font-size: 0.875rem;
  color: #212529;
  text-align: left;
  list-style: none;
  background-color: #fff;
  background-clip: padding-box;
  border: 1px solid #e1e3ea;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: none;

  &.show {
    display: block !important;
  }

  .dropdown-item {
    display: block;
    width: 100%;
    padding: 0.75rem 1rem;
    clear: both;
    font-weight: 400;
    color: #212529;
    text-align: inherit;
    text-decoration: none;
    white-space: nowrap;
    background-color: transparent;
    border: 0;
    cursor: pointer;

    &:hover, &:focus {
      background-color: #f8f9fa;
      color: #1e2125;
    }

    &.text-danger {
      color: #dc3545;

      &:hover, &:focus {
        background-color: rgba(220, 53, 69, 0.1);
        color: #dc3545;
      }
    }

    i {
      width: 16px;
      text-align: center;
    }
  }

  .dropdown-divider {
    height: 0;
    margin: 0.5rem 0;
    overflow: hidden;
    border-top: 1px solid #e1e3ea;
  }
}

// Form controls
.form-control {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }

  &.ps-10 {
    padding-left: 2.5rem;
  }
}

.form-select {
  border-radius: 8px;
  border: 1px solid #e1e3ea;

  &:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
  }
}

// Utility classes
.text-warning {
  color: #ffc107 !important;
}

.fs-4 {
  font-size: 1.25rem !important;
}

// Permissions Modal Styling
.modal-content {
  border-radius: 12px;
  border: none;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #e1e3ea;
  border-radius: 12px 12px 0 0;
  padding: 1.5rem;

  .modal-title {
    color: #2d3748;
    font-weight: 700;
  }
}

.modal-body {
  padding: 1.5rem;
  max-height: 60vh;
  overflow-y: auto;
}

.modal-footer {
  background-color: #f8f9fa;
  border-top: 1px solid #e1e3ea;
  border-radius: 0 0 12px 12px;
  padding: 1rem 1.5rem;
}

// Permission Cards
.permission-card {
  border: 1px solid #e1e3ea;
  border-radius: 8px;
  transition: all 0.2s ease;

  &:hover {
    border-color: #0d6efd;
    box-shadow: 0 2px 8px rgba(13, 110, 253, 0.15);
  }

  .form-check {
    margin-bottom: 0;

    .form-check-input {
      width: 1.25rem;
      height: 1.25rem;
      margin-top: 0.125rem;
      border: 2px solid #e1e3ea;
      border-radius: 4px;

      &:checked {
        background-color: #0d6efd;
        border-color: #0d6efd;
      }

      &:focus {
        border-color: #0d6efd;
        box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
      }
    }

    .form-check-label {
      cursor: pointer;
      margin-left: 0.5rem;
    }
  }
}

// Select All Checkbox
#selectAll {
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid #e1e3ea;
  border-radius: 4px;

  &:checked {
    background-color: #198754;
    border-color: #198754;
  }

  &:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
  }
}
