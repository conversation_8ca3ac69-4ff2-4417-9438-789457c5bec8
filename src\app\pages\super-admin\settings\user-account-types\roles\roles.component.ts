import { ChangeDetectorRef, Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  // Sample roles data
  roles: any[] = [];
  openDropdownId: number | null = null;

  constructor(private router: Router, private settingService: settingService, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.lodingAllRoles();
  }

lodingAllRoles() {
    this.settingService.getAllRolls().subscribe({
      next: (response) => {
        console.log('API Response:', response);
           this.roles = response.data;
           this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.roles = [];
        this.cd.detectChanges();
      }
    });
  }

  addRole(): void {
    console.log('Add new role');
  }

  editRole(id: number): void {
    console.log('Edit role:', id);
  }



  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);
  }




  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

  toggleDropdown(roleId: number): void {
    this.openDropdownId = this.openDropdownId === roleId ? null : roleId;
  }

  isDropdownOpen(roleId: number): boolean {
    return this.openDropdownId === roleId;
  }

}
