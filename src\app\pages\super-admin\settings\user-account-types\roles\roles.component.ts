import { ChangeDetectorRef, Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  // Sample roles data
  roles: any[] = [];
  openDropdownId: number | null = null;
  selectedRole: any = null;
  originalPermissions: any[] = [];

  constructor(private router: Router, private settingService: settingService, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.lodingAllRoles();
  }

lodingAllRoles() {
    this.settingService.getAllRolls().subscribe({
      next: (response) => {
        console.log('API Response:', response);
        this.roles = response.data || [];

        // Add sample permissions if they don't exist (for testing)
        this.roles = this.roles.map(role => ({
          ...role,
          permissions: role.permissions || [
            { id: 1, name: 'View Users', description: 'Can view user list and details', isSelected: true },
            { id: 2, name: 'Create Users', description: 'Can create new users', isSelected: true },
            { id: 3, name: 'Edit Users', description: 'Can edit user information', isSelected: false },
            { id: 4, name: 'Delete Users', description: 'Can delete users from system', isSelected: false },
            { id: 5, name: 'View Reports', description: 'Can access system reports', isSelected: true },
            { id: 6, name: 'Manage Settings', description: 'Can modify system settings', isSelected: false }
          ]
        }));

        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.roles = [];
        this.cd.detectChanges();
      }
    });
  }

  addRole(): void {
    console.log('Add new role');
  }

  editRole(id: number): void {
    console.log('Edit role:', id);
  }



  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);

    // Find the role by id
    const role = this.roles.find(r => r.id === id);
    if (role) {
      // Create a deep copy of the role and its permissions
      this.selectedRole = JSON.parse(JSON.stringify(role));

      // Initialize permissions if they don't exist
      if (!this.selectedRole.permissions) {
        this.selectedRole.permissions = [];
      }

      // Add isSelected property to each permission if it doesn't exist
      this.selectedRole.permissions = this.selectedRole.permissions.map((permission: any) => ({
        ...permission,
        isSelected: permission.isSelected !== undefined ? permission.isSelected : true
      }));

      // Store original permissions for comparison
      this.originalPermissions = JSON.parse(JSON.stringify(this.selectedRole.permissions));

      // Open the modal
      const modal = document.getElementById('permissionsModal');
      if (modal) {
        const bootstrapModal = new (window as any).bootstrap.Modal(modal);
        bootstrapModal.show();
      }
    }
  }




  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

  toggleDropdown(roleId: number): void {
    this.openDropdownId = this.openDropdownId === roleId ? null : roleId;
  }

  isDropdownOpen(roleId: number): boolean {
    return this.openDropdownId === roleId;
  }

  // Permissions Modal Methods
  areAllPermissionsSelected(): boolean {
    if (!this.selectedRole?.permissions || this.selectedRole.permissions.length === 0) {
      return false;
    }
    return this.selectedRole.permissions.every((permission: any) => permission.isSelected);
  }

  toggleAllPermissions(event: any): void {
    const isChecked = event.target.checked;
    if (this.selectedRole?.permissions) {
      this.selectedRole.permissions.forEach((permission: any) => {
        permission.isSelected = isChecked;
      });
    }
  }

  onPermissionChange(): void {
    // This method is called when individual permission checkbox changes
    // You can add any additional logic here if needed
    this.cd.detectChanges();
  }

  savePermissions(): void {
    if (!this.selectedRole) {
      return;
    }

    console.log('Saving permissions for role:', this.selectedRole.name);
    console.log('Updated permissions:', this.selectedRole.permissions);

    // Here you would typically call an API to save the permissions
    // For now, we'll just update the local role data
    const roleIndex = this.roles.findIndex(r => r.id === this.selectedRole.id);
    if (roleIndex !== -1) {
      this.roles[roleIndex].permissions = [...this.selectedRole.permissions];
    }

    // Close the modal
    const modal = document.getElementById('permissionsModal');
    if (modal) {
      const bootstrapModal = (window as any).bootstrap.Modal.getInstance(modal);
      if (bootstrapModal) {
        bootstrapModal.hide();
      }
    }

    // Show success message (you can replace this with your preferred notification method)
    alert('Permissions updated successfully!');
  }

}
