import { ChangeDetectorRef, Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  // Sample roles data
  roles: any[] = [];
  openDropdownId: number | null = null;
  selectedRole: any = null;
  originalPermissions: any[] = [];
  showPermissionsModal: boolean = false;

  // All available permissions in the system
  allPermissions: string[] = [
    "user_actions", "list_users", "view_user", "create_user", "update_user", "delete_user",
    "developer_actions", "list_developers", "view_developer", "create_developer", "update_developer", "delete_developer",
    "view_statistics", "list_broker_maps", "create_broker_maps", "list_contracts", "view_contract", "create_contract", "update_contract",
    "location_actions", "project_actions", "list_projects", "create_project", "view_project", "update_project", "delete_project",
    "request_actions", "list_requests", "create_request", "view_request", "update_request", "delete_request",
    "unit_actions", "list_units", "view_unit", "create_unit", "update_unit", "delete_unit",
    "excel_actions", "download_template", "upload_excel", "export_excel",
    "complaint_actions", "list_complaints", "view_complaint", "create_complaint", "delete_complaint",
    "reply_actions", "list_reply", "create_reply", "create_permission", "create_role", "grant_permission_to_role", "revoke_permission_to_role"
  ];

  constructor(private router: Router, private settingService: settingService, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.lodingAllRoles();
  }

lodingAllRoles() {
    this.settingService.getAllRolls().subscribe({
      next: (response) => {
        console.log('API Response:', response);
        this.roles = response.data || [];

        // Convert permissions array to objects with isSelected property
        this.roles = this.roles.map(role => ({
          ...role,
          permissions: role.permissions ? role.permissions.map((permission: any) => {
            // If permission is already an object, keep it as is
            if (typeof permission === 'object' && permission.name) {
              return {
                ...permission,
                isSelected: permission.isSelected !== undefined ? permission.isSelected : true
              };
            }
            // If permission is a string, convert it to object
            else if (typeof permission === 'string') {
              return {
                id: permission,
                name: permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                description: `Permission to ${permission.replace(/_/g, ' ')}`,
                isSelected: true
              };
            }
            return permission;
          }) : []
        }));

        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.roles = [];
        this.cd.detectChanges();
      }
    });
  }

  addRole(): void {
    console.log('Add new role');
  }

  editRole(id: number): void {
    console.log('Edit role:', id);
  }



  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);

    // Find the role by id
    const role = this.roles.find(r => r.id === id);
    if (role) {
      // Create a deep copy of the role
      this.selectedRole = JSON.parse(JSON.stringify(role));

      // Get current role permissions as array of strings
      const currentPermissions = this.selectedRole.permissions || [];

      // Create permissions array with all available permissions
      this.selectedRole.permissions = this.allPermissions.map(permission => {
        const isSelected = currentPermissions.includes(permission);
        return {
          id: permission,
          name: permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
          description: `Permission to ${permission.replace(/_/g, ' ')}`,
          isSelected: isSelected
        };
      });

      // Store original permissions for comparison
      this.originalPermissions = JSON.parse(JSON.stringify(this.selectedRole.permissions));

      // Show the modal
      this.showPermissionsModal = true;
    }
  }




  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

  toggleDropdown(roleId: number): void {
    this.openDropdownId = this.openDropdownId === roleId ? null : roleId;
  }

  isDropdownOpen(roleId: number): boolean {
    return this.openDropdownId === roleId;
  }

  // Permissions Modal Methods
  areAllPermissionsSelected(): boolean {
    if (!this.selectedRole?.permissions || this.selectedRole.permissions.length === 0) {
      return false;
    }
    return this.selectedRole.permissions.every((permission: any) => permission.isSelected);
  }

  toggleAllPermissions(event: any): void {
    const isChecked = event.target.checked;
    if (this.selectedRole?.permissions) {
      this.selectedRole.permissions.forEach((permission: any) => {
        permission.isSelected = isChecked;
      });
    }
  }

  onPermissionChange(): void {

    this.cd.detectChanges();
  }

  savePermissions(): void {
    if (!this.selectedRole) return;

    // Get only selected permissions
    const selectedPermissions = this.selectedRole.permissions
      .filter((permission: any) => permission.isSelected)
      .map((permission: any) => permission.id);

    console.log('Sending permissions to API:', selectedPermissions);

    this.settingService.updateRolePermissions(this.selectedRole.id, selectedPermissions).subscribe({
      next: () => {
        // Update local data with only selected permissions
        const roleIndex = this.roles.findIndex(r => r.id === this.selectedRole.id);
        if (roleIndex !== -1) {
          this.roles[roleIndex].permissions = selectedPermissions;
        }

        this.closePermissionsModal();
        Swal.fire('Permissions updated successfully!');
      },
      error: (error) => {
        console.error('Error:', error);
        Swal.fire('Error updating permissions!').then(() => {
          this.closePermissionsModal();
        });
      }
    });
  }

  closePermissionsModal(): void {
    this.showPermissionsModal = false;
    this.selectedRole = null;
  }

}
