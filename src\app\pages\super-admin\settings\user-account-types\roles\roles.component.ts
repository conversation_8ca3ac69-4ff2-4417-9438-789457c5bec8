import { ChangeDetectorRef, Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  // Sample roles data
  roles: any[] = [];
  openDropdownId: number | null = null;
  selectedRole: any = null;
  originalPermissions: any[] = [];
  showPermissionsModal: boolean = false;
  updatedPermissions: string[] = [];
  formChanged: boolean = false;

  // All available permissions from API
  allPermissions: string[] = [];

  constructor(private router: Router, private settingService: settingService, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.lodingAllRoles();
    this.loadAllPermissions();
  }

lodingAllRoles() {
    this.settingService.getAllRolls().subscribe({
      next: (response) => {
        console.log('API Response:', response);
        this.roles = response.data || [];

        // Convert permissions array to objects with isSelected property
        this.roles = this.roles.map(role => ({
          ...role,
          permissions: role.permissions ? role.permissions.map((permission: any) => {
            // If permission is already an object, keep it as is
            if (typeof permission === 'object' && permission.name) {
              return {
                ...permission,
                isSelected: permission.isSelected !== undefined ? permission.isSelected : true
              };
            }
            // If permission is a string, convert it to object
            else if (typeof permission === 'string') {
              return {
                id: permission,
                name: permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                description: `Permission to ${permission.replace(/_/g, ' ')}`,
                isSelected: true
              };
            }
            return permission;
          }) : []
        }));

        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.roles = [];
        this.cd.detectChanges();
      }
    });
  }

  addRole(): void {
    console.log('Add new role');
  }

  editRole(id: number): void {
    console.log('Edit role:', id);
  }



  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);

    // Find the role by id
    const role = this.roles.find(r => r.id === id);
    if (role) {
      // Create a deep copy of the role and its permissions
      this.selectedRole = JSON.parse(JSON.stringify(role));

      // Initialize permissions if they don't exist
      if (!this.selectedRole.permissions) {
        this.selectedRole.permissions = [];
      }

      // Ensure permissions are in the correct format
      this.selectedRole.permissions = this.selectedRole.permissions.map((permission: any) => {
        // If permission is already an object, keep it as is
        if (typeof permission === 'object' && permission.name) {
          return {
            ...permission,
            isSelected: permission.isSelected !== undefined ? permission.isSelected : true
          };
        }
        // If permission is a string, convert it to object
        else if (typeof permission === 'string') {
          return {
            id: permission,
            name: permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            description: `Permission to ${permission.replace(/_/g, ' ')}`,
            isSelected: true
          };
        }
        return permission;
      });

      // Store original permissions for comparison
      this.originalPermissions = JSON.parse(JSON.stringify(this.selectedRole.permissions));

      // Show the modal (simple way)
      this.showPermissionsModal = true;
    }
  }




  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

  toggleDropdown(roleId: number): void {
    this.openDropdownId = this.openDropdownId === roleId ? null : roleId;
  }

  isDropdownOpen(roleId: number): boolean {
    return this.openDropdownId === roleId;
  }

  // Permissions Modal Methods
  areAllPermissionsSelected(): boolean {
    if (!this.selectedRole?.permissions || this.selectedRole.permissions.length === 0) {
      return false;
    }
    return this.selectedRole.permissions.every((permission: any) => permission.isSelected);
  }

  toggleAllPermissions(event: any): void {
    const isChecked = event.target.checked;
    if (this.selectedRole?.permissions) {
      this.selectedRole.permissions.forEach((permission: any) => {
        permission.isSelected = isChecked;
      });
    }
  }

  onPermissionChange(): void {

    this.cd.detectChanges();
  }

  savePermissions(): void {
    if (!this.selectedRole) return;

    console.log('Sending permissions to API:', this.updatedPermissions);

    this.settingService.updateRolePermissions(this.selectedRole.id, this.updatedPermissions).subscribe({
      next: () => {
        // Update local data with updated permissions
        const roleIndex = this.roles.findIndex(r => r.id === this.selectedRole.id);
        if (roleIndex !== -1) {
          this.roles[roleIndex].permissions = [...this.updatedPermissions];
        }

        this.closePermissionsModal();
        Swal.fire('Permissions updated successfully!');
      },
      error: (error) => {
        console.error('Error:', error);
        Swal.fire('Error updating permissions!').then(() => {
          this.closePermissionsModal();
        });
      }
    });
  }

  closePermissionsModal(): void {
    this.showPermissionsModal = false;
    this.selectedRole = null;
  }

  loadAllPermissions(): void {
    this.settingService.getAllPermissions().subscribe({
      next: (response) => {
        console.log('Permissions API Response:', response);

        // Extract permission names from API response
        if (response && response.data) {
          this.allPermissions = response.data.map((permission: any) => permission.name || permission.permission_name || permission);
        } else if (response && Array.isArray(response)) {
          this.allPermissions = response.map((permission: any) => permission.name || permission.permission_name || permission);
        } else {
          console.warn('Unexpected permissions response structure:', response);
          this.allPermissions = [];
        }

        console.log('All permissions loaded:', this.allPermissions);
      },
      error: (error) => {
        console.error('Error loading permissions:', error);
        // Fallback to static permissions if API fails
        this.allPermissions = [
          "user_actions", "list_users", "view_user", "create_user", "update_user", "delete_user",
          "developer_actions", "list_developers", "view_developer", "create_developer", "update_developer", "delete_developer",
          "view_statistics", "list_broker_maps", "create_broker_maps", "list_contracts", "view_contract", "create_contract", "update_contract",
          "location_actions", "project_actions", "list_projects", "create_project", "view_project", "update_project", "delete_project",
          "request_actions", "list_requests", "create_request", "view_request", "update_request", "delete_request",
          "unit_actions", "list_units", "view_unit", "create_unit", "update_unit", "delete_unit",
          "excel_actions", "download_template", "upload_excel", "export_excel",
          "complaint_actions", "list_complaints", "view_complaint", "create_complaint", "delete_complaint",
          "reply_actions", "list_reply", "create_reply", "create_permission", "create_role", "grant_permission_to_role", "revoke_permission_to_role"
        ];
      }
    });
  }

  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);

    // Find the role by id
    const role = this.roles.find(r => r.id === id);
    if (role) {
      // Create a deep copy of the role
      this.selectedRole = JSON.parse(JSON.stringify(role));

      // Initialize updatedPermissions with current role permissions
      this.updatedPermissions = [...(this.selectedRole.permissions || [])];
      this.formChanged = false;

      // Show the modal
      this.showPermissionsModal = true;
    }
  }

  closePermissionsModal(): void {
    this.showPermissionsModal = false;
    this.selectedRole = null;
    this.updatedPermissions = [];
    this.formChanged = false;
  }

  // Handle permission checkbox change
  onPermissionChange(permission: string, event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;

    if (checked) {
      // Add permission if not exists
      if (!this.updatedPermissions.includes(permission)) {
        this.updatedPermissions.push(permission);
      }
    } else {
      // Remove permission
      this.updatedPermissions = this.updatedPermissions.filter(p => p !== permission);
    }

    this.formChanged = true;
  }

  // Check if all permissions are selected
  areAllPermissionsSelected(): boolean {
    return this.allPermissions.every(permission => this.updatedPermissions.includes(permission));
  }

  // Toggle all permissions
  toggleAllPermissions(event: Event): void {
    const checked = (event.target as HTMLInputElement).checked;

    if (checked) {
      this.updatedPermissions = [...this.allPermissions];
    } else {
      this.updatedPermissions = [];
    }

    this.formChanged = true;
  }

  // Format permission name for display
  formatPermissionName(permission: string): string {
    return permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
  }

}
