import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  // Sample roles data
  roles: any[] = [];

  constructor(private router: Router, private settingService: settingService, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.lodingAllRoles();
  }

lodingAllRoles() {
    this.settingService.getAllRolls().subscribe({
      next: (response) => {
        console.log('API Response:', response);

        // Check if response has data property
        if (response && response.data) {
          this.roles = response.data;
        } else if (response && Array.isArray(response)) {
          // If response is directly an array
          this.roles = response;
        } else {
          console.warn('Unexpected response structure:', response);
          this.roles = [];
        }

        console.log('Roles array:', this.roles);
        this.cd.detectChanges(); // Force change detection
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.roles = [];
        this.cd.detectChanges();
      }
    });
  }

  addRole(): void {
    console.log('Add new role');
  }

  editRole(id: number): void {
    console.log('Edit role:', id);
  }

  deleteRole(id: number): void {
    if (confirm('Are you sure you want to delete this role?')) {
      this.roles = this.roles.filter(role => role.id !== id);
    }
  }

  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);
  }



  getRoleBadgeClass(color: string): string {
    return `badge-light-${color}`;
  }

  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

}
