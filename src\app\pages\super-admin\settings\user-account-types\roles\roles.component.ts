import { ChangeDetectorRef, Component, OnInit, HostListener } from '@angular/core';
import { Router } from '@angular/router';
import { settingService } from '../../../services/Setting.service';
import Swal from 'sweetalert2';

@Component({
  selector: 'app-roles',
  templateUrl: './roles.component.html',
  styleUrls: ['./roles.component.scss']
})
export class RolesComponent implements OnInit {

  // Sample roles data
  roles: any[] = [];
  openDropdownId: number | null = null;
  selectedRole: any = null;
  originalPermissions: any[] = [];
  showPermissionsModal: boolean = false;

  constructor(private router: Router, private settingService: settingService, private cd: ChangeDetectorRef) { }

  ngOnInit(): void {
    this.lodingAllRoles();
  }

lodingAllRoles() {
    this.settingService.getAllRolls().subscribe({
      next: (response) => {
        console.log('API Response:', response);
        this.roles = response.data || [];

        // Convert permissions array to objects with isSelected property
        this.roles = this.roles.map(role => ({
          ...role,
          permissions: role.permissions ? role.permissions.map((permission: any) => {
            // If permission is already an object, keep it as is
            if (typeof permission === 'object' && permission.name) {
              return {
                ...permission,
                isSelected: permission.isSelected !== undefined ? permission.isSelected : true
              };
            }
            // If permission is a string, convert it to object
            else if (typeof permission === 'string') {
              return {
                id: permission,
                name: permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                description: `Permission to ${permission.replace(/_/g, ' ')}`,
                isSelected: true
              };
            }
            return permission;
          }) : []
        }));

        this.cd.detectChanges();
      },
      error: (error) => {
        console.error('Error loading roles:', error);
        this.roles = [];
        this.cd.detectChanges();
      }
    });
  }

  addRole(): void {
    console.log('Add new role');
  }

  editRole(id: number): void {
    console.log('Edit role:', id);
  }



  viewPermissions(id: number): void {
    console.log('View permissions for role:', id);

    // Find the role by id
    const role = this.roles.find(r => r.id === id);
    if (role) {
      // Create a deep copy of the role and its permissions
      this.selectedRole = JSON.parse(JSON.stringify(role));

      // Initialize permissions if they don't exist
      if (!this.selectedRole.permissions) {
        this.selectedRole.permissions = [];
      }

      // Ensure permissions are in the correct format
      this.selectedRole.permissions = this.selectedRole.permissions.map((permission: any) => {
        // If permission is already an object, keep it as is
        if (typeof permission === 'object' && permission.name) {
          return {
            ...permission,
            isSelected: permission.isSelected !== undefined ? permission.isSelected : true
          };
        }
        // If permission is a string, convert it to object
        else if (typeof permission === 'string') {
          return {
            id: permission,
            name: permission.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            description: `Permission to ${permission.replace(/_/g, ' ')}`,
            isSelected: true
          };
        }
        return permission;
      });

      // Store original permissions for comparison
      this.originalPermissions = JSON.parse(JSON.stringify(this.selectedRole.permissions));

      // Show the modal (simple way)
      this.showPermissionsModal = true;
    }
  }




  goBack(): void {
    this.router.navigate(['/super-admin/user-account-types']);
  }

  toggleDropdown(roleId: number): void {
    this.openDropdownId = this.openDropdownId === roleId ? null : roleId;
  }

  isDropdownOpen(roleId: number): boolean {
    return this.openDropdownId === roleId;
  }

  // Permissions Modal Methods
  areAllPermissionsSelected(): boolean {
    if (!this.selectedRole?.permissions || this.selectedRole.permissions.length === 0) {
      return false;
    }
    return this.selectedRole.permissions.every((permission: any) => permission.isSelected);
  }

  toggleAllPermissions(event: any): void {
    const isChecked = event.target.checked;
    if (this.selectedRole?.permissions) {
      this.selectedRole.permissions.forEach((permission: any) => {
        permission.isSelected = isChecked;
      });
    }
  }

  onPermissionChange(): void {

    this.cd.detectChanges();
  }

  savePermissions(): void {
    if (!this.selectedRole) return;

    const selectedPermissions = this.selectedRole.permissions
      .filter((permission: any) => permission.isSelected)
      .map((permission: any) => permission.id || permission.name);

    this.settingService.updateRolePermissions(this.selectedRole.id, selectedPermissions).subscribe({
      next: () => {
        // Update local data
        const roleIndex = this.roles.findIndex(r => r.id === this.selectedRole.id);
        if (roleIndex !== -1) {
          this.roles[roleIndex].permissions = [...this.selectedRole.permissions];
        }

        this.closePermissionsModal();
        Swal.fire('Permissions updated successfully!');
      },
      error: (error) => {
        console.error('Error:', error);
        Swal.fire('Error updating permissions!').then(() => {
          this.closePermissionsModal();
        });
      }
    });
  }

  closePermissionsModal(): void {
    this.showPermissionsModal = false;
    this.selectedRole = null;
  }

}
